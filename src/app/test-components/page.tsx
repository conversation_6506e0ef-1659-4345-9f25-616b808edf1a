"use client";

import { useState } from "react";
import { AppInput } from "../components/app-input";
import { AppButton } from "../components/app-button";
import { AppTypography } from "../components/app-typography";
import { FaGithub, FaSearch } from "react-icons/fa";

export default function TestComponents() {
  const [inputValue, setInputValue] = useState("");
  const [inputWithError, setInputWithError] = useState("");
  const [mode, setMode] = useState<"dark" | "light">("dark");

  return (
    <div className={`min-h-screen p-8 ${mode === "dark" ? "bg-gray-900" : "bg-white"}`}>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Mode Toggle */}
        <div className="flex gap-4 mb-8">
          <AppButton
            variant={mode === "dark" ? "primary" : "outline"}
            onClick={() => setMode("dark")}
          >
            Dark Mode
          </AppButton>
          <AppButton
            variant={mode === "light" ? "primary" : "outline"}
            onClick={() => setMode("light")}
          >
            Light Mode
          </AppButton>
        </div>

        {/* Typography Tests */}
        <section className="space-y-4">
          <AppTypography variant="highlight-1" className={mode === "dark" ? "text-white" : "text-black"}>
            Typography Test - Highlight 1
          </AppTypography>
          <AppTypography variant="title-3-strong" className={mode === "dark" ? "text-white/80" : "text-black/80"}>
            Title 3 Strong
          </AppTypography>
          <AppTypography variant="body-2" className={mode === "dark" ? "text-white/60" : "text-black/60"}>
            Body 2 text for testing
          </AppTypography>
        </section>

        {/* Button Tests */}
        <section className="space-y-4">
          <h2 className={`text-xl font-bold ${mode === "dark" ? "text-white" : "text-black"}`}>
            Button Tests
          </h2>
          <div className="flex gap-4 flex-wrap">
            <AppButton variant="primary" size="default">
              Primary Default
            </AppButton>
            <AppButton variant="primary" size="lg">
              Primary Large
            </AppButton>
            <AppButton variant="secondary" size="default">
              Secondary Default
            </AppButton>
            <AppButton variant="outline" size="default">
              Outline Default
            </AppButton>
            <AppButton variant="primary" disabled>
              Disabled
            </AppButton>
            <AppButton variant="primary" leftIcon={<FaGithub />}>
              With Icon
            </AppButton>
          </div>
        </section>

        {/* Input Tests */}
        <section className="space-y-4">
          <h2 className={`text-xl font-bold ${mode === "dark" ? "text-white" : "text-black"}`}>
            Input Tests
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Dark Mode Inputs */}
            <div className="space-y-4">
              <h3 className={`text-lg font-semibold ${mode === "dark" ? "text-white" : "text-black"}`}>
                {mode === "dark" ? "Dark Mode" : "Light Mode"} Inputs
              </h3>
              
              <AppInput
                mode={mode}
                label="Default Input"
                placeholder="Enter text here"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
              />
              
              <AppInput
                mode={mode}
                label="Input with Icon"
                placeholder="Search..."
                icon={FaSearch}
              />
              
              <AppInput
                mode={mode}
                label="Input with Error"
                placeholder="This has an error"
                value={inputWithError}
                onChange={(e) => setInputWithError(e.target.value)}
                error="This field is required"
              />
              
              <AppInput
                mode={mode}
                label="Disabled Input"
                placeholder="Disabled input"
                disabled
                value="Disabled value"
              />
            </div>

            {/* Alternative Mode for Comparison */}
            <div className="space-y-4">
              <h3 className={`text-lg font-semibold ${mode === "dark" ? "text-white" : "text-black"}`}>
                {mode === "dark" ? "Light Mode" : "Dark Mode"} Comparison
              </h3>
              
              <AppInput
                mode={mode === "dark" ? "light" : "dark"}
                label="Default Input"
                placeholder="Enter text here"
              />
              
              <AppInput
                mode={mode === "dark" ? "light" : "dark"}
                label="Input with Icon"
                placeholder="Search..."
                icon={FaSearch}
              />
              
              <AppInput
                mode={mode === "dark" ? "light" : "dark"}
                label="Input with Error"
                placeholder="This has an error"
                error="This field is required"
              />
              
              <AppInput
                mode={mode === "dark" ? "light" : "dark"}
                label="Disabled Input"
                placeholder="Disabled input"
                disabled
                value="Disabled value"
              />
            </div>
          </div>
        </section>

        {/* Signing Page Preview */}
        <section className="space-y-4">
          <h2 className={`text-xl font-bold ${mode === "dark" ? "text-white" : "text-black"}`}>
            Signing Page Preview
          </h2>
          <div 
            className="rounded-[20px] bg-[#1A1F23] px-[48px] py-[40px] border border-white/10 max-w-[720px]"
          >
            <div className="flex flex-col items-center gap-1 mb-10">
              <AppTypography 
                variant="highlight-1" 
                className="text-[72px] font-bold leading-[0.89] text-[#DFDBFA] text-center"
                style={{ fontFamily: 'Blender Pro' }}
              >
                Welcome to Recon
              </AppTypography>
            </div>

            <div className="flex flex-col gap-3 mb-10">
              <AppTypography 
                variant="title-3-strong" 
                className="text-[20px] font-bold leading-[1.3] text-white/80"
                style={{ fontFamily: 'Blender Pro' }}
              >
                Log in
              </AppTypography>
              <AppButton
                variant="primary"
                size="lg"
                fullWidth
                leftIcon={<FaGithub size={24} />}
                className="bg-[#DFDBFA] text-[#5649B0] border-[#DFDBFA] hover:bg-[#DFDBFA]/90 px-3 py-2 rounded-lg font-bold text-[24px] leading-[0.83] justify-center items-center gap-1"
                style={{ fontFamily: 'Blender Pro' }}
              >
                Sign in with Github
              </AppButton>
            </div>

            <div className="flex flex-col gap-3">
              <AppTypography 
                variant="title-3-strong" 
                className="text-[20px] font-bold leading-[1.3] text-white/80"
                style={{ fontFamily: 'Blender Pro' }}
              >
                Try with Recon with no login
              </AppTypography>
              <AppButton
                variant="outline"
                size="lg"
                fullWidth
                className="bg-[#7160E8] border-[#DFDBFA] text-[#DFDBFA] hover:bg-[#7160E8]/90 px-3 py-2 rounded-lg font-bold text-[24px] leading-[0.83] justify-center items-center"
                style={{ fontFamily: 'Blender Pro' }}
              >
                Scaffold invariants Sandbox
              </AppButton>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
